# How to enable new pipelines

Put next into .gitlab-ci.yml and configure variables

```
image: "asia-docker.pkg.dev/inspectorio-ant/platform-docker-images/stable/python:3.9-legacy" # base image

include:
  - project: "devops/ci-cd-templates"
    file: "generic-pipeline.yaml"
    ref: $HELM_CHART_BRANCH

variables:
  DEV_LANGUAGE: python
  CI_LINT_CHECKS: "false"
  CI_SECURITY_STATIC_ANALYZER_CHECKS: "true"
  CI_SECURITY_CVE_CHECKS: "true"
  CI_SECURITY_GIT_SECRETS_CHECKS: "true"
  CI_SECURITY_STATIC_ANALYZER_CHECKS_EXTENDED: "true"
  BASE_IMAGE_DOCKERFILE: Dockerfile_deps
  BASE_IMAGE_DEPS: go.mod Dockerfile Dockerfile_deps .gitlab-ci.yml
  APP_NAME: mrnotifybot
  CHART_TYPE: cluster
  HELM_CHART: chart/
  ARGOCD_LOGS_TAIL: 20 #tail size for pod logs in deploy stage
  ARGOCD_SYNC_WAIT_TIMEOUT: 600 # time to wait for success deploy
```

Create `Dockerfile_deps` and define building base image. https://gitlab.inspectorio.com/devops/mrnotifybot/-/blob/master/Dockerfile_deps as an example.

Define paths to !!!files!!! that relay on content of `BASE_IMAGE_DEPS`. Dependencies, etc.
Use next approach in Dockerfile to allow using base image

```
ARG  BASE_IMAGE

FROM ${BASE_IMAGE}
```

# Chart preparation

Request an access and clone the project: https://gitlab.inspectorio.com/devops/gitops-monorepo
Put chart content into `HELM_CHART` folder. Use https://gitlab.inspectorio.com/devops/gitops-monorepo/-/tree/master/charts/generic-charts/generic-application-v1 as a template.

Optionally define a name of application `APP_NAME`. Default to repository name. Helm chart name should be equal to `APP_NAME`.

Define `CHART_TYPE`. _saas_ for business-level apps, _cluster_ for infra level

# Gitops Monorepo

Add values file in format of `<project>.yaml` inside of folder values/`CHART_TYPE`(saas?)/`APP_NAME`/. Structure of file should be taken from https://gitlab.inspectorio.com/devops/gitops-monorepo/-/blob/master/charts/generic-charts/generic-application-v1/default-stg.yaml and adopted to requirements of application. Please be careful, provided file is just an example what you can do!

# Gitops CD repo

The repository is intended to allow application to be deployed to specific destination via GitOps approach.
Create a fresh clone https://gitlab.inspectorio.com/devops/gitops-cd
Open <env>/bootstrap/values.yaml file and configure 'projects section'. <env> must be the same you previously defined in a name of values file `<namespace>-<env>.yaml`.

```
System variables:
ARGOCD_IMAGE_VERSION: v2.5.2
ARGOCD_PASSWORD:
ARGOCD_USERNAME:
HELM_GAR_LOCATION: asia
HELM_GAR_PROJECT: inspectorio-ant
HELM_GAR_REPOSITORY: gcp-helm-repository
GITOPS_PIPELINE_TOKEN # owned by service account: sa-gitlab-gitops-pipeline , set in Admin Area > Settings > CICD 
```
