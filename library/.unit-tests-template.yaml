.base_unit_tests:
  stage: tests
  before_script:
    - |
      # SSH Add Gitlab Deploy Keys: https://docs.gitlab.com/ee/ci/ssh_keys/
      command -v ssh-agent >/dev/null || ( apt-get update -y && apt-get install openssh-client -y )
      eval $(ssh-agent -s)
      echo "${SSH_PRIVATE_KEY}" | tr -d '\r' | ssh-add - > /dev/null
      mkdir -p ~/.ssh && chmod 700 ~/.ssh
      ssh-keyscan -t rsa github.com bitbucket.org gitlab.inspectorio.com 2>/dev/null > ~/.ssh/known_hosts
      chmod 644 ~/.ssh/known_hosts
  retry:
    max: 1
    when:
      - api_failure
      - runner_system_failure
      - scheduler_failure
      - unknown_failure
      - stale_schedule


.base_test_report:
  extends: .monorepo
  stage: test-report
  script: python3 ${SCRIPTS_ROOT}${CI_SCRIPTS_PATH}/aio_cycle.py
  rules:
    - if: $AIO_REPORT_ENABLED == "true" && $CI_PIPELINE_SOURCE == "merge_request_event"
  allow_failure: True
