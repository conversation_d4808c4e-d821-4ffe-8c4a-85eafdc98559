.diff:
  image: $CD_IMAGE
  stage: diff
  cache: {}
  variables:
    GIT_STRATEGY: $CD_GIT_STRATEGY
  extends: .monorepo
  before_script:
    - |
      if [[ ! -z "${SCRIPTS_ROOT}" ]]; then
        mkdir -p ${SCRIPTS_ROOT}
        #download values
        curl -H "PRIVATE-TOKEN:$GITOPS_PIPELINE_TOKEN" --create-dirs "$CI_API_V4_URL/projects/$CI_ARTIFACTS_REPO/repository/archive?path=$CI_VALUES_PATH&sha=${CI_GITOPS_MONOREPO_BRANCH:-master}" -o /tmp/archive.tar.gz
        tar zxf /tmp/archive.tar.gz -C ${SCRIPTS_ROOT}  --strip-components 1
      fi;
      if [[ $RELEASE_CHANNEL == "preview" ]]; then  # If merge request
        export PREVIEW_SUBPATH="preview/${CI_COMMIT_REF_SLUG}/"
        if [[ -z $MIGRATION_ARGOCD_NAMING ]] || [[ ! -z $MIGRATION_ARGOCD_NAMING && $MIGRATION_ARGOCD_NAMING == "False" ]];then
          export RELEASE_NAME=pr-${APP_NAME}-${PROJECT}-${ENV}-${CI_MERGE_REQUEST_IID}
        else
          export RELEASE_NAME=pr-${APP_NAME}-${ENV}-${CI_MERGE_REQUEST_IID}
        fi
      else
        if [[ -z $MIGRATION_ARGOCD_NAMING ]] || [[ ! -z $MIGRATION_ARGOCD_NAMING && $MIGRATION_ARGOCD_NAMING == "False" ]];then
          export RELEASE_NAME=${APP_NAME}-${PROJECT}-${ENV}
        else
          export RELEASE_NAME=${APP_NAME}-${ENV}
        fi
        export PREVIEW_SUBPATH="chart/"
      fi
      CHART_PACKAGE="${APP_NAME}-${GitVersion_SemVer}.tgz"
      PROJECT_PATH="projects/${ENV}/${PROJECT}/${APP_NAME}/${PREVIEW_SUBPATH}"
      PROJECT_ROOT_PATH="gitops-cd/${PROJECT_PATH}"
      HELM_CHART_VALUES="${SCRIPTS_ROOT}values/${CHART_TYPE}/${APP_NAME}/${VARIANT}.yaml"
      KUSTOMIZATION_GENERATE_SCRIPT="${SCRIPTS_ROOT}scripts/kustomize_generator.py"
  script:
    - |
      retry_command() {
          local command="$1"
          local max_attempts="$2"
          local attempt=1

          while [ $attempt -le $max_attempts ]; do
              echo "Attempt $attempt: $command"
              set +x
              eval "$command"
              exit_code=$?
              set -x

              if [ $exit_code -eq 0 ]; then
                  echo "Command succeeded on attempt $attempt."
                  return 0
              else
                  echo "Command failed on attempt $attempt (Exit code: $exit_code). Retrying..."
                  random_number=$(( $RANDOM % 5 )) # sleep random seconds below 5s
                  sleep ${random_number}
              fi

              attempt=$((attempt + 1))
          done

          echo "Command failed after $max_attempts attempts."
          exit 1
      }
    - gcloud auth configure-docker ${HELM_GAR_LOCATION}-docker.pkg.dev --quiet
    - retry_command "argocd --plaintext --insecure login $ARGOCD_SERVER_URL --insecure --username $ARGOCD_USERNAME --password $ARGOCD_PASSWORD" $ARGO_RETRY_MAX_ATTEMPTS
    - helm pull oci://${HELM_GAR_LOCATION}-docker.pkg.dev/${HELM_GAR_PROJECT}/${HELM_GAR_REPOSITORY}/${RELEASE_CHANNEL}/${APP_NAME} --version ${GitVersion_SemVer}
    - git clone --depth 3 --recurse-submodules --jobs=4 https://${CI_USER}:${GITOPS_PIPELINE_TOKEN}@${CI_SERVER_HOST}/devops/gitops-cd.git
    - |
      set -x
      mkdir -p $PROJECT_ROOT_PATH
      rm -rf $PROJECT_ROOT_PATH/*
      tar -xvzf $CHART_PACKAGE -C $PROJECT_ROOT_PATH --strip-components=1
      rm $CHART_PACKAGE
      cp $HELM_CHART_VALUES $PROJECT_ROOT_PATH/ops-values.yaml
      if [[ ! -z "$ASSETS_PATH" ]] && [[ ! -z "$ASSETS_DST_PATH" ]]; then
        mkdir -p gitops-cd${ASSETS_DST_PATH}/${PROJECT}
        cp -rf ${SCRIPTS_ROOT}${ASSETS_PATH}/${PROJECT} gitops-cd/${ASSETS_DST_PATH}/
      fi
      # Generate kustomization.yaml
    - |
      cd gitops-cd
      if [[ ! -z "$ASSETS_PATH" ]] && [[ ! -z "$ASSETS_DST_PATH" ]]; then
      git add ${ASSETS_DST_PATH}
      fi
      git diff
      set +e
      set -o pipefail

      retries=3
      while true; do
        argocd_app_name=$(argocd --plaintext --insecure app list | awk '{print $1}' | grep "argocd/${RELEASE_NAME:0:63}$" )
        exit_code=$?
        if [ $exit_code -eq 0 ]; then
          break
        elif [ $exit_code -ne 0 ] && [ $retries -eq 0 ]; then
          echo "$RELEASE_NAME Application is not present on cluster yet".
          exit 1
        fi
        retries=$(($retries - 1))
      done

      set -e
      retry_times=1
      while true; do
        argocd --plaintext --insecure app diff $argocd_app_name --local ${PROJECT_PATH} --exit-code=false
        exit_code=$?
        if [[ $exit_code -eq 0 ]]; then
          break
        elif [[ ${retry_times} -ge ${ARGO_RETRY_MAX_ATTEMPTS} ]]; then
          echo "Reached maximum retry attempts (${ARGO_RETRY_MAX_ATTEMPTS}). Exiting with exit code $exit_code."
          exit $exit_code
        fi

        random_number=$(( $RANDOM % 5 )) # sleep random seconds below 5s
        echo "Retrying argocd diff (attempt ${retry_times})..."
        sleep ${random_number}
        retry_times=$(( $retry_times + 1 ))
      done
  retry:
    max: 1
    when: runner_system_failure

.full-diff:
  extends: .diff
  variables:
    GIT_STRATEGY: fetch
