.run-gcp-call-cronjob:
  stage: utils
  image: $CD_IMAGE
  script:
    - |
      RELEASE_NAME=${RELEASE_NAME:-$CI_PROJECT_NAME}
      kubectl -n ${NAMESPACE:-default} create job --from=cronjob/${RELEASE_NAME}-cronjob-${CRONJOB_NAME} ${RELEASE_NAME}-cronjob-${CRONJOB_NAME}-`date +%s`
  tags:
    - gke-${ENV}
  environment:
    name: gcp-${ENV}

.run-gcp-stg-call-cronjob:
  extends: .run-gcp-call-cronjob
  variables:
    ENV: stg
  rules:
    - if: '$CI_COMMIT_REF_NAME == $CI_DEFAULT_BRANCH && $JOB_ACTION == "cronjob"'

.run-gcp-pre-call-cronjob:
  extends: .run-gcp-call-cronjob
  variables:
    ENV: pre
  rules:
    - if: '$CI_COMMIT_REF_NAME =~ /^release-v\d+\.\d+\.\d+$/ && $JOB_ACTION == "cronjob"'

.run-gcp-prd-call-cronjob:
  extends: .run-gcp-call-cronjob
  variables:
    ENV: prd
  rules:
    - if: '$CI_COMMIT_TAG  =~ /^v\d+\.\d+\.\d+$/ && $JOB_ACTION == "cronjob"'
