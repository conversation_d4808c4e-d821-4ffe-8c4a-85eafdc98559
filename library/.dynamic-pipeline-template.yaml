.generate-config:
  stage: build
  extends: .generate-pipeline
  script: python3 ${SCRIPTS_ROOT}${CI_SCRIPTS_PATH}/pipeline_generator.py --app-type $CHART_TYPE --app-name $APP_NAME  --channel $RELEASE_CHANNEL --when ${WHEN:-none} > dynamic-cd.yaml
  artifacts:
    paths:
      - dynamic-cd.yaml
  rules:
    - if: "$DOCKER_BUILD_ONLY"
      when: never
    - if: "$CI_COMMIT_REF_NAME == $CI_DEFAULT_BRANCH"
    - if: $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "$CI_DEFAULT_BRANCH" && $CI_MERGE_REQUEST_LABELS =~ /cd-poc/
    - if: "$CI_MERGE_REQUEST_IID && $CI_MERGE_REQUEST_LABELS =~ /preview/"
    - if: '$CI_COMMIT_REF_NAME =~ /^release-v\d+\.\d+\.\d+$/'
    - if: '$CI_COMMIT_TAG  =~ /^v\d+\.\d+\.\d+$/'

.delivery-job:
  variables: # trick with injecting dotenv variables into downstream pipeline
    GitVersion_SemVer: $GitVersion_SemVer
    APP_NAME: $APP_NAME
    DKR_TAG: $DKR_TAG
    REGISTRY_HOST: $REGISTRY_HOST
    PROJECT_ID: $PROJECT_ID
    DKR_REPOSITORY: $DKR_REPOSITORY
    SCRIPTS_ROOT: $SCRIPTS_ROOT
    RELEASE_CHANNEL: $RELEASE_CHANNEL
    CHART_TYPE: $CHART_TYPE
  stage: release
  trigger:
    include:
      - artifact: dynamic-cd.yaml
        job: generate-config
    strategy: depend
  allow_failure: true
  rules:
    - if: "$DOCKER_BUILD_ONLY"
      when: never
    - if: "$CI_COMMIT_REF_NAME == $CI_DEFAULT_BRANCH"
    - if: "$CI_MERGE_REQUEST_IID && $CI_MERGE_REQUEST_LABELS =~ /preview/"
    - if: $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "$CI_DEFAULT_BRANCH" && $CI_MERGE_REQUEST_LABELS =~ /cd-poc/
    - if: '$CI_COMMIT_REF_NAME =~ /^release-v\d+\.\d+\.\d+$/'
    - if: '$CI_COMMIT_TAG  =~ /^v\d+\.\d+\.\d+$/'
