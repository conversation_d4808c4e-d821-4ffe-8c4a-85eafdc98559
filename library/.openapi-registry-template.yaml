.openapi-schema-build:
  stage: build
  image:
    name: $CI_REDOCLY_IMAGE
  allow_failure: true
  script:
    - |
      check_multi_version_support_for_domain_yaml() {
        domain=$1
        if ls ${OPENAPI_PATH}/${domain}.v*.yaml > /dev/null 2>&1; then
          eval ${domain}_multi_version_support=true
        else
          eval ${domain}_multi_version_support=false
        fi
      }
      send_slack_notification() {
        APP_NAME=$1
        NOTIFICATION_MESSAGE="The openapi schema definition file for \`${APP_NAME}\` service is not found!"
        SLACK_NOTIFICATION_CHANNEL=$OPENAPI_SLACK_NOTIFICATION_CHANNEL
        URL=https://slack.com/api/chat.postMessage
        curl -X POST -H "Authorization: Bearer $GITOPS_SLACK_TOKEN" -H 'Content-type: application/json' --data "{\"blocks\":[{\"type\":\"section\",\"text\":{\"type\":\"mrkdwn\",\"text\":\"$NOTIFICATION_MESSAGE\"}}], \"channel\": \"$SLACK_NOTIFICATION_CHANNEL\",\"username\":\"openapi\"}" $URL
      }
      build_openapi_schema() {
        echo "Building OpenAPI schema"
        domains="integration public internal admin"
        #check schema definition file for service
        if  ! ls ${OPENAPI_PATH}/integration*.yaml > /dev/null 2>&1  &&  ! ls ${OPENAPI_PATH}/public*.yaml > /dev/null 2>&1 && ! ls ${OPENAPI_PATH}/admin*.yaml > /dev/null 2>&1 && ! ls ${OPENAPI_PATH}/internal*.yaml > /dev/null 2>&1 ; then
          # TODO, ie, send remind to some slack channel
          echo "Schema definition file for ${APP_NAME} service is not found"
          send_slack_notification $APP_NAME
          return 0
        fi
        for domain in $(echo $domains | tr " " "\n"); do
          check_multi_version_support_for_domain_yaml $domain
        done
        for domain in $(echo $domains | tr " " "\n"); do
          newv=$(eval echo \${${domain}_multi_version_support})
          echo "domain $domain has multiple version support is $newv"
        done
        for domain in $(echo $domains | tr " " "\n"); do
          newv=$(eval echo \${${domain}_multi_version_support})
          echo "here $domain and $newv"
          if [ ! $newv == true ]; then
            if [ -f ${OPENAPI_PATH}/${domain}.yaml ]; then
              redocly bundle -o artifacts/${domain}.json --lint ${OPENAPI_PATH}/${domain}.yaml
              # remove the word openapi from filename to make it consistent
              if [[ `echo artifacts/${domain}.json` =~ openapi ]]; then
                mv artifacts/${domain}.json "`echo artifacts/${domain}.json | sed 's/openapi.//'`"
              else
                echo "The openapi word not found in filename  ${domain}"
              fi
            fi
          else
            schema_files=`ls ${OPENAPI_PATH}/${domain}.v*.yaml`
            for file in $schema_files; do
              #remove last suffix from filename
              tmp_file_name=`basename $file|cut -f 1-2 -d '.'` 
              echo "tmp_file_name is $tmp_file_name $file"
              redocly bundle -o artifacts/${tmp_file_name}.json --lint $file
              if [[ `echo artifacts/${tmp_file_name}.json` =~ openapi ]]; then
                mv artifacts/${tmp_file_name}.json "`echo artifacts/${tmp_file_name}.json | sed 's/openapi.//'`"
              else
                echo "The openapi word not found in filename ${domain} for multiple version"
              fi
            done
          fi
        done

        
        ls -ltr artifacts/
      }
      apk add curl
      build_openapi_schema
  artifacts:
    paths:
      - artifacts/*.json
  retry:
    max: 1
    when:
      - api_failure
      - runner_system_failure
  rules:
    - if: $CHART_TYPE == "saas" && $CI_COMMIT_REF_NAME == $CI_DEFAULT_BRANCH && $RUN_OPENAPI_REGISTRY != "false"
    - if: $CHART_TYPE == "saas" && $CI_PIPELINE_SOURCE == "merge_request_event" && $RUN_OPENAPI_REGISTRY != "false"
    - if: $CHART_TYPE == "saas" && $CI_COMMIT_TAG  =~ /^v\d+\.\d+\.\d+$/ && $RUN_OPENAPI_REGISTRY != "false"

.openapi-schema-publish:
  stage: build
  needs: 
    - job: openapi-schema-build
      artifacts: true
    - job: determine-version
      artifacts: true
  image:
    name: $CD_IMAGE
  allow_failure: true
  script:
    - |
      check_multi_version_support_for_domain_json() {
        domain=$1
        if ls artifacts/${domain}.v*.json > /dev/null 2>&1; then
           eval ${domain}_multi_version_support=true
        else
           eval ${domain}_multi_version_support=false
        fi
      }
      get_product() {
        service=$1
        declare -A products
        products["qrm"]="integration-api labsync master-data remote-config car emlauncher-be mobileresponder notimanager technical-docs sight-be report-html"
        products["dna"]="airflow defect-recommend-be document-validator product-risk-be analytic3 pub-capa"
        products["css"]="fms passport-be plapbe bouncers hermes-be sms chatbot-be"
        products["pm"]="tracking"
        products["infra"]="infrabot devops-exporter"
        #Get product name for the service in the associate array
        for product in ${!products[@]}; do 
          #echo "products[$product] = ${products[$product]}"
          if [[ ${products[$product]} =~ $service ]]; then
            echo $product
          fi
        done
      }
      create_artifact() {
        group=$1
        domain=$2
        file=$3
        response=`curl -s ${APICURIO_BASE_URL}/apis/registry/v2/groups/${group}/artifacts`
        if [[ $response =~ "ENABLED" ]] && [[ $response =~ $domain ]]; then
          echo "Already registry the artifact for the $domain in $group for first time, skip remaining!"
          return 0
        fi
        curl -X POST -H "Content-Type: application/json; artifactType=OPENAPI" -H "X-Registry-ArtifactId: $domain" \
        ${APICURIO_BASE_URL}/apis/registry/v2/groups/${group}/artifacts -d @$file || true
      }
      publish_openapi_schema() {
        DEFAULT_APICURIO_GROUP=misc
        domains="integration public internal admin"
        # product=$( get_product $APP_NAME )
        # echo "The product for $APP_NAME is $product"
        # get service apicurio group variable from saas repo
        if [ -z $APICURIO_GROUP ]; then
          echo "Not able to find the group name from saas repo, use default!"
          registry_group=$DEFAULT_APICURIO_GROUP
        else
          registry_group=$APICURIO_GROUP
        fi
        if ! ls artifacts/*.json > /dev/null 2>&1 ;then
          echo "artifacts schema json file not found for ${APP_NAME}"
          return 0
        else
          ls -ltr artifacts/*.json
        fi
        for domain in $(echo $domains | tr " " "\n"); do
          check_multi_version_support_for_domain_json $domain
        done
        for domain in $(echo $domains | tr " " "\n"); do
          newv=$(eval echo \${${domain}_multi_version_support})
          echo "domain $domain has multiple version support is $newv"
        done
        for domain in $(echo $domains | tr " " "\n"); do
          newv=$(eval echo \${${domain}_multi_version_support})
          if [ ! $newv == true -a -f artifacts/$domain.json ]; then
            create_artifact $registry_group $domain artifacts/$domain.json
            echo "Delete previous version [${RELEASE_CHANNEL}] if exists"
            curl --request DELETE "${APICURIO_BASE_URL}/apis/registry/v2/groups/${registry_group}/artifacts/${domain}/versions/${RELEASE_CHANNEL}" || true
            echo "Publish to apicurio registry for version ${RELEASE_CHANNEL}"
            curl --request POST "${APICURIO_BASE_URL}/apis/registry/v2/groups/${registry_group}/artifacts/${domain}/versions" \
            --fail-with-body \
            --header "Content-Type: application/json; artifactType=OPENAPI" \
            --header "Accept: application/json" \
            --header "X-Registry-Version: ${RELEASE_CHANNEL}" \
            -d @artifacts/${domain}.json || true
          elif [ $newv == true ]; then 
            schema_files=`ls artifacts/${domain}.v*.json`
            for file in $schema_files; do
              create_artifact $registry_group $domain $file
              #remove last suffix from filename
              version=`basename $file|cut -f 2 -d '.'`
              echo "Delete previous version [${version}-${RELEASE_CHANNEL}] if exists"
              curl --request DELETE "${APICURIO_BASE_URL}/apis/registry/v2/groups/${registry_group}/artifacts/${domain}/versions/${version}-${RELEASE_CHANNEL}" || true
              echo "Publish to apicurio registry for version ${version}-${RELEASE_CHANNEL}"
              curl --request POST "${APICURIO_BASE_URL}/apis/registry/v2/groups/${registry_group}/artifacts/${domain}/versions" \
              --fail-with-body \
              --header "Content-Type: application/json; artifactType=OPENAPI" \
              --header "Accept: application/json" \
              --header "X-Registry-Version: ${version}-${RELEASE_CHANNEL}" \
              -d @$file || true
            done
          fi
        done
      }
      publish_openapi_schema
  retry:
    max: 1
    when:
      - api_failure
      - runner_system_failure
  rules:
    - if: $CHART_TYPE == "saas" && $CI_COMMIT_REF_NAME == $CI_DEFAULT_BRANCH && $RUN_OPENAPI_REGISTRY != "false"
    - if: $CHART_TYPE == "saas" && $CI_COMMIT_TAG  =~ /^v\d+\.\d+\.\d+$/ && $RUN_OPENAPI_REGISTRY != "false"
