.dockerize:
  stage: build
  image:
    name: $CI_BUILD_IMAGE
    entrypoint: [""]
  script:
    - |
      build_image() {
        # Just an interface to build images
        if [[ "$KANIKO_CACHING_ENABLED" == true ]]; then
          KAN<PERSON>KO_CACHING_OPTIONS="--cache=true --cache-ttl=${KANIKO_IMAGE_CACHING_TTL:-24h}"
        fi
        if [[ "$KANIKO_COMPRESSED_CACHE" == false ]]; then
          # https://github.com/GoogleContainerTools/kaniko#flag---compressed-caching
          KANIKO_CACHING_OPTIONS="${KANIKO_CACHING_OPTIONS} --compressed-caching=false"
        fi
        echo "kaniko caching options: ${KANIKO_CACHING_OPTIONS}"


        IMAGE_LABELS="--label org.opencontainers.image.vendor=$CI_SERVER_URL/$GITLAB_USER_LOGIN
          --label org.opencontainers.image.authors=$CI_SERVER_URL/$GITLAB_USER_LOGIN
          --label org.opencontainers.image.revision=$CI_COMMIT_SHA
          --label org.opencontainers.image.source=$CI_PROJECT_URL
          --label org.opencontainers.image.documentation=$CI_PROJECT_URL
          --label org.opencontainers.image.licenses=$CI_PROJECT_URL
          --label org.opencontainers.image.url=$CI_PROJECT_URL
          --label vcs-url=$CI_PROJECT_URL
          --label com.gitlab.ci.user=$CI_SERVER_URL/$GITLAB_USER_LOGIN
          --label com.gitlab.ci.email=$GITLAB_USER_EMAIL
          --label com.gitlab.ci.tagorbranch=$CI_COMMIT_REF_NAME
          --label com.gitlab.ci.pipelineurl=$CI_PIPELINE_URL
          --label com.gitlab.ci.commiturl=$CI_PROJECT_URL/commit/$CI_COMMIT_SHA
          --label com.gitlab.ci.cijoburl=$CI_JOB_URL
          --label com.gitlab.ci.mrurl=$CI_PROJECT_URL/-/merge_requests/$CI_MERGE_REQUEST_ID"

        #Build date for opencontainers
        BUILDDATE="'$(date '+%FT%T%z' | sed -E -n 's/(\+[0-9]{2})([0-9]{2})$/\1:\2/p')'" #rfc 3339 date
        IMAGE_LABELS="$IMAGE_LABELS --label org.opencontainers.image.created=$BUILDDATE --label build-date=$BUILDDATE"
        #Description for opencontainers
        BUILDTITLE=$(echo $CI_PROJECT_TITLE | tr " " "_")
        IMAGE_LABELS="$IMAGE_LABELS --label org.opencontainers.image.title=$BUILDTITLE --label org.opencontainers.image.description=$BUILDTITLE"
        #Add ref.name for opencontainers
        IMAGE_LABELS="$IMAGE_LABELS --label org.opencontainers.image.ref.name=${DKR_REPOSITORY}:${DKR_TAG}"

        if [[ "$CLOUD_BUILD" == "true" ]]; then
          _format() {
            sed -r -e 's/(--[a-z-]+) /\1=/g' -e 's/[[:blank:]]+-/\n-/g' | sed -r -e '/^$/d' -e 's/^(--)/  - \1/'
          }

          gcp_region=$(\
            curl http://metadata.google.internal/computeMetadata/v1/instance/zone \
              --silent \
              --header 'Metadata-Flavor: Google' \
            | cut -d/ -f4 | cut -d- -f1-2)

          cat > .gcloudignore <<EOF
      #!include:.gitignore
      .git
      EOF
          cat > cloudbuild.yaml <<EOF
      timeout: ${CLOUD_BUILD_TIMEOUT:-600s}
      steps:
      - name: gcr.io/cloud-builders/docker
        args:
        - build
        - --file=${DOCKER_FILE_PATH:-Dockerfile}
        - --build-arg=SSH_PRIVATE_KEY=\$\$SSH_PRIVATE_KEY
        - --tag=$DKR_REPOSITORY:$DKR_TAG
        - --tag=$DKR_REPOSITORY:latest
      $(echo "$DKR_ADDITIONAL_ARGS" | _format)
      $(echo "$IMAGE_LABELS" | _format)
        - '.'
        secretEnv: ["SSH_PRIVATE_KEY"]
      images:
      - $DKR_REPOSITORY:$DKR_TAG
      - $DKR_REPOSITORY:latest
      availableSecrets:
        secretManager:
        - versionName: projects/inspectorio-ant/secrets/ssh-private-key/versions/latest
          env: SSH_PRIVATE_KEY
      EOF

          args="--region=${CLOUD_BUILD_REGION:-$gcp_region} "
          if [[ -n "$CLOUD_BUILD_MACHINE_TYPE" ]]; then
            args+="--machine-type=$CLOUD_BUILD_MACHINE_TYPE "
          fi

          gcloud builds submit $args
        else
          /kaniko/executor ${DKR_ADDITIONAL_ARGS} \
                          --build-arg SSH_PRIVATE_KEY="${SSH_PRIVATE_KEY}" \
                          --context dir://. \
                          --dockerfile "${DOCKER_FILE_PATH:-Dockerfile}" \
                          --destination "${DOCKER_IMAGE_NAME:-$DKR_REPOSITORY}:${DKR_TAG}" \
                          --destination "${DOCKER_IMAGE_NAME:-$DKR_REPOSITORY}:latest" \
                          --log-timestamp=true  \
                          $KANIKO_CACHING_OPTIONS \
                          $IMAGE_LABELS
        fi
      }
      #Inject a name of image into build.env for further use
      echo "APP_IMAGE=${DOCKER_IMAGE_NAME:-$DKR_REPOSITORY}:${DKR_TAG}" >> build.env
      echo "RELEASE_CHANNEL=${RELEASE_CHANNEL}" >> build.env

      echo "Checking for changed dependencies"
      set +e
      if [ ! -z "${BASE_IMAGE_DOCKERFILE}" ]; then
        echo "Checking for changed dependencies"
        set +e
        IMAGE_DEPS_HASH=`md5sum $BASE_IMAGE_DEPS | md5sum | awk '{print $1}' | xargs `
        BASE_IMAGE="${DKR_REPOSITORY_BASE_IMAGE}:${IMAGE_DEPS_HASH}"
        IMAGE_EXISTS=`gcrane manifest "${BASE_IMAGE}"`
        EXIT_CODE=$?
        echo "BASE_IMAGE=$BASE_IMAGE" >> build.env
        if [ "$EXIT_CODE" != 0 ]; then
          echo "Building base image because of changed dependencies"
          ORIGINAL_DOCKERFILE_PATH=$DOCKER_FILE_PATH
          export DOCKER_FILE_PATH=${BASE_IMAGE_DOCKERFILE}
          ORIGINAL_DKR_TAG=$DKR_TAG
          ORIGINAL_DKR_REPOSITORY=$DKR_REPOSITORY
          export DKR_REPOSITORY=${DKR_REPOSITORY_BASE_IMAGE}
          export DKR_TAG=$IMAGE_DEPS_HASH
          export DOCKER_FILE_PATH=${BASE_IMAGE_DOCKERFILE}
          export DKR_ADDITIONAL_ARGS="${DKR_ADDITIONAL_ARGS}"
          echo "DKR_ADDITIONAL_ARGS: ${DKR_ADDITIONAL_ARGS}"
          build_image
          export DOCKER_FILE_PATH=$ORIGINAL_DOCKERFILE_PATH
          export DKR_TAG=$ORIGINAL_DKR_TAG
          export DKR_REPOSITORY=$ORIGINAL_DKR_REPOSITORY
        else
          echo "Base image already exists. Skipping base image build."
        fi
      fi

      # We consider a format of $DOCKER_FILE_PATH as : DockerfilePath:name-of-image Dockerfile2Path:name-of-image2
      # This approach allows us to define and use several dockerfiles for a project
      # All images will be built with the same tag
      IFS=' ' read -ra pairs <<< ${DOCKER_FILE_PATH:-"Dockerfile:"} # set default dockerfile path to Dockerfile and suffix of image to empty string
      for pair in "${pairs[@]}"; do

          # Split each pair by the colon (:)
          IFS=':' read -ra keyValue <<< "$pair"

          # Get the key and value
          export DOCKER_FILE_PATH="${keyValue[0]}"
          export DOCKER_IMAGE_SUFFIX="${keyValue[1]}"

          # Print the key and value
          echo "DOCKER_FILE_PATH: $DOCKER_FILE_PATH, DOCKER_IMAGE_SUFFIX: $DOCKER_IMAGE_SUFFIX"

          set +e
          if [ "${DOCKER_IMAGE_SUFFIX}" ]; then
            export DOCKER_IMAGE_NAME="${DKR_REPOSITORY}-${DOCKER_IMAGE_SUFFIX}"
          else
            export DOCKER_IMAGE_NAME="${DKR_REPOSITORY}"
          fi

          # Inject env variables with names of containers into build.env
          echo "DOCKER_IMAGE_${DOCKER_IMAGE_SUFFIX:-main}=${DOCKER_IMAGE_NAME}:${DKR_TAG}" >> build.env

          echo "Build & Push ${DOCKER_IMAGE_NAME}:${DKR_TAG}"
          echo "Check image existence"
          IMAGE_EXISTS=`gcrane manifest "${DOCKER_IMAGE_NAME}:${DKR_TAG}"`
          EXIT_CODE=$?
          set -e

          if [ "$EXIT_CODE" == 0 ] && [ "$FORCE_BUILD_IMAGE" != "true" ]; then
            echo "Image ${DOCKER_IMAGE_NAME}:${DKR_TAG} already exists. Finishing."
            continue
          fi

          # pass additional variables to docker build to be able to use them in Dockerfile
          # BASE_IMAGE: name of base image with dependencies"
          # COMMIT_SHA: commit sha of current commit
          # IMAGE_NAME: name of image
          # DOCKER_REPOSITORY: original name of image without suffix
          export DKR_ADDITIONAL_ARGS="${DKR_ADDITIONAL_ARGS} --build-arg BASE_IMAGE=${BASE_IMAGE} --build-arg COMMIT_SHA=${CI_COMMIT_SHA} --build-arg IMAGE_NAME=${DOCKER_IMAGE_NAME} --build-arg DOCKER_REPOSITORY=${DKR_REPOSITORY} --build-arg RELEASE_CHANNEL=${RELEASE_CHANNEL} --build-arg NGINX_FRONTEND_BASE_IMAGE=${NGINX_FRONTEND_BASE_IMAGE} --build-arg SEMANTIC_VERSION=${GitVersion_SemVer}"
          echo "Image ${DOCKER_IMAGE_NAME}:${DKR_TAG} does not exist. Building..."
          echo "DKR_ADDITIONAL_ARGS: ${DKR_ADDITIONAL_ARGS}"
          build_image
      done

  retry:
    max: 1
    when:
      - api_failure
      - runner_system_failure
  tags:
    - gke-docker-gcp-ant
  cache: {}
  artifacts:
    reports:
      #propagates variables into the pipeline level
      dotenv: build.env
  rules:
    - if: '$CHART_TYPE == "saas" && $CI_COMMIT_TAG  =~ /^v\d+\.\d+\.\d+$/'
    - if: $CHART_TYPE == "saas" && $CI_COMMIT_REF_NAME == $CI_DEFAULT_BRANCH
    - if: $CHART_TYPE == "saas" && $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: '$CHART_TYPE == "saas" && $CI_COMMIT_REF_NAME =~ /^release-v\d+\.\d+\.\d+$/'
