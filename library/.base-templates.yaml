.monorepo:
  image: $CD_IMAGE
  before_script:
    - |
      if [ ! -z "${SCRIPTS_ROOT}" ]; then 
        mkdir ${SCRIPTS_ROOT}

        # We use curl to download only single directory script and branch master for fast and non-leftover config in gitops-monorepo
        curl -H "PRIVATE-TOKEN:$GITOPS_PIPELINE_TOKEN" --create-dirs "$CI_API_V4_URL/projects/$CI_ARTIFACTS_REPO/repository/archive?path=$CI_SCRIPTS_PATH&sha=${CI_GITOPS_MONOREPO_BRANCH:-master}" -o /tmp/archive.tar.gz
        tar zxf /tmp/archive.tar.gz -C ${SCRIPTS_ROOT} --strip-components 1
        curl -H "PRIVATE-TOKEN:$GITOPS_PIPELINE_TOKEN" --create-dirs "$CI_API_V4_URL/projects/$CI_ARTIFACTS_REPO/repository/archive?path=$CI_VALUES_PATH&sha=${CI_GITOPS_MONOREPO_BRANCH:-master}" -o /tmp/archive.tar.gz
        tar zxf /tmp/archive.tar.gz -C ${SCRIPTS_ROOT} --strip-components 1
      fi
  retry:
    max: 2
    when: runner_system_failure

.generate-pipeline:
  extends: .monorepo
  variables:
    GIT_STRATEGY: fetch
  artifacts:
    paths:
      - dynamic-cd.yaml
