.gitversion_function:
  image:
    name: asia-docker.pkg.dev/inspectorio-ant/mirror/python/python:3.12.4-slim
    entrypoint: [""]
  stage: .pre
  variables:
    GIT_STRATEGY: fetch
    GIT_DEPTH: 0 # force a deep/non-shallow fetch need by gitversion
  cache: [] # caches and before / after scripts can mess things up
  before_script: []
  after_script: []
  script:
    - |
      echo "Gather setting version in pipeline variables using dotenv artifacts"
      if [[ $CHART_TYPE == "saas" ]]; then
        echo SCRIPTS_ROOT=gitops-monorepo/  >> thisversion.env
      fi

      # Support Inspectorio Git trunk-based deployment strategy
      if [[ "${CI_COMMIT_TAG}" =~ ^v[0-9]+.[0-9]+.[0-9]+$ ]];then
        export RELEASE_CHANNEL="stable"
        export GitVersion_SemVer="$(echo $CI_COMMIT_TAG | tr -d 'v')"
      elif [[ "${CI_COMMIT_REF_NAME}" =~ ^release-v[0-9]+.[0-9]+.[0-9]+$ ]];then
        export RELEASE_CHANNEL="rc"
        export GitVersion_SemVer="$(echo $CI_COMMIT_REF_NAME | tr -d 'release\-v')-${RELEASE_CHANNEL}"
      elif [[ "${CI_COMMIT_REF_NAME}" == "master" || "${CI_COMMIT_REF_NAME}" == "main" || "${CI_COMMIT_REF_NAME}" == "develop" ]];then
        export RELEASE_CHANNEL="dev"
        export GitVersion_SemVer="1.0.0-${RELEASE_CHANNEL}-${CI_COMMIT_SHORT_SHA}" # helm package required SemVersion format
      else
        # Cover Merge Request ci build (without preview labels)
        export RELEASE_CHANNEL="dev"
        export GitVersion_SemVer="0.1.0-${RELEASE_CHANNEL}-${CI_COMMIT_SHORT_SHA}" # helm package required SemVersion format
      fi

      if [[ $CI_MERGE_REQUEST_LABELS =~ preview ]]; then
        export RELEASE_CHANNEL=preview
        export GitVersion_SemVer="1.0.0-${RELEASE_CHANNEL}-${CI_COMMIT_SHORT_SHA}" # helm package required SemVersion format
      fi

      if [[ "${APP_NAME}" == "gitops-monorepo" ]];then
        export RELEASE_CHANNEL="stable"
        export GitVersion_SemVer="1.0.0-${RELEASE_CHANNEL}-${CI_COMMIT_SHORT_SHA}" # helm package required SemVersion format
      fi

      # Print out information for troubleshooting
      echo "REGISTRY_HOST=asia.gcr.io" >> thisversion.env
      echo "GitVersion_SemVer=${GitVersion_SemVer}" >> thisversion.env
      echo "APP_NAME=${APP_NAME:-${CI_PROJECT_NAME}}" >> thisversion.env
      echo "DKR_TAG=${CI_COMMIT_SHA}" >> thisversion.env
      echo "PROJECT_ID=inspectorio-ant" >> thisversion.env
      echo "RELEASE_CHANNEL=$RELEASE_CHANNEL" >> thisversion.env
      echo "RELEASE_BRANCH_NAME=${CI_COMMIT_REF_NAME}" >> thisversion.env
      echo "RELEASE_COMMIT_TAG=${CI_COMMIT_TAG:-none}" >> thisversion.env
      echo DKR_REPOSITORY="${ARTIFACT_REGISTRY_LOCATION}-docker.pkg.dev/${ARTIFACT_REGISTRY_PROJECT_ID}/${ARTIFACT_REGISTRY_REPOSITORY}/${RELEASE_CHANNEL}/${APP_NAME}" >> thisversion.env
      echo DKR_REPOSITORY_BASE_IMAGE="${ARTIFACT_REGISTRY_LOCATION}-docker.pkg.dev/${ARTIFACT_REGISTRY_PROJECT_ID}/${ARTIFACT_REGISTRY_REPOSITORY}/base/${APP_NAME}" >> thisversion.env
      cat thisversion.env

  artifacts:
    reports:
      dotenv: thisversion.env
  allow_failure: false
  rules:
    - when: on_success
      if: $GitVersion_SemVer == null
