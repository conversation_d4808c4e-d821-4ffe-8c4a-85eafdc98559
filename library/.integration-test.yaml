.post-integration-test:
  stage: post-tests
  trigger:
    project: $CI_INTEGRATION_TEST_PROJECT
    branch: $CI_INTEGRATION_TEST_BRANCH
    strategy: depend
  allow_failure: true
  rules:
    - if: $CI_COMMIT_REF_NAME == $CI_DEFAULT_BRANCH && $CI_INTEGRATION_TEST_ENABLE == 'true'
      when: on_success
    - if: $CI_COMMIT_REF_NAME =~ /^release-v\d+\.\d+\.\d+$/ && $CI_INTEGRATION_TEST_ENABLE == 'true'
      when: on_success
    - if: $CI_COMMIT_TAG  =~ /^v\d+\.\d+\.\d+$/ && $CI_INTEGRATION_TEST_ENABLE == 'true'
      when: on_success
  variables:
    UPSTREAM_CI_INTEGRATION_TEST_ENABLE: ${CI_INTEGRATION_TEST_ENABLE}
    UPSTREAM_CI_INTEGRATION_TEST_PROJECT: ${CI_INTEGRATION_TEST_PROJECT}
    UPSTREAM_CI_INTEGRATION_TEST_BRANCH: ${CI_INTEGRATION_TEST_BRANCH}
    UPSTREAM_CI_APP_NAME: ${APP_NAME}
    UPSTREAM_CI_COMMIT_REF_NAME: $CI_COMMIT_REF_NAME
    UPSTREAM_CI_PIPELINE_URL: $CI_PIPELINE_URL
