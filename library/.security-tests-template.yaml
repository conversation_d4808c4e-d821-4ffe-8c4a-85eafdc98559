include: library/.base-templates.yaml

.security-checks:
  stage: security-tests
  retry:
    max: 1
    when:
      - api_failure
      - runner_system_failure
      - scheduler_failure
      - unknown_failure
      - stale_schedule

.security-checks-bandit:
  extends: .security-checks
  image: $SECURITY_SCAN_IMAGE

.security-cve-checks-python:
  stage: security-checks
  extends: .security-checks-bandit
  script:
    - |
      echo "checking for python lock"
      if [ -f "Pipfile.lock" ]; then
        echo "Pipfile.lock found"
        set -x
        if [ "$CI_SECURITY_CVE_CHECKS_IGNORE" = "true" ]; then
          pipenv lock --requirements | safety check --full-report --stdin --continue-on-error
        else
          pipenv lock --requirements | safety check --full-report --stdin
        fi
        set +x
      elif [ -f "poetry.lock" ]; then
        echo "poetry.lock found"
        set -x
        if [ "$CI_SECURITY_CVE_CHECKS_IGNORE" = "true" ]; then
          poetry export --without-hashes | safety check --full-report --stdin --continue-on-error
        else
          poetry export --without-hashes | safety check --full-report --stdin
        fi
        set +x
      else
        set -x
        if [ "$CI_SECURITY_CVE_CHECKS_IGNORE" = "true" ]; then
          pip freeze | safety check --full-report --stdin --continue-on-error
        else
          pip freeze | safety check --full-report --stdin
        fi
        set +x
      fi
  rules:
    - if: $CI_SECURITY_CVE_CHECKS == "true" && $DEV_LANGUAGE == "python" && $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_SECURITY_CVE_CHECKS == "true" && $DEV_LANGUAGE == "python" && $CI_COMMIT_TAG == ""

.security-cve-checks-javascript:
  stage: security-checks
  allow_failure: true
  script:
    - npm audit --production --audit-level=high --json > security_cve_checks.json
  artifacts:
    when: on_failure
    paths:
      - security_cve_checks.json
    expire_in: 1 week
  rules:
    - if: '$CI_SECURITY_CVE_CHECKS == "true" && $DEV_LANGUAGE == "javascript" && $CI_PIPELINE_SOURCE == "merge_request_event"'
    - if: '$CI_SECURITY_CVE_CHECKS == "true" && $DEV_LANGUAGE == "javascript" && $CI_COMMIT_TAG == ""'

.security-static-analyzer-python:
  stage: security-checks
  extends: .security-checks-bandit
  script:
    - touch .bandit.baseline
    - bandit -r -a vuln -l -ii --ini .bandit -b .bandit.baseline
  rules:
    - if: '$CI_SECURITY_STATIC_ANALYZER_CHECKS == "true" && $DEV_LANGUAGE == "python" && $CI_PIPELINE_SOURCE == "merge_request_event"'
    - if: '$CI_SECURITY_STATIC_ANALYZER_CHECKS == "true" && $DEV_LANGUAGE == "python" && $CI_COMMIT_TAG == ""'

.security-static-analyzer-python-extended:
  stage: security-checks
  extends: .security-checks-bandit
  script:
    - touch .bandit.baseline
    - bandit -r app -a vuln -f json --ini .bandit -b .bandit.baseline > security_static_analyzer_checks.json
  artifacts:
    when: on_failure
    paths:
      - security_static_analyzer_checks.json
    expire_in: 1 week
  rules:
    - if: '$CI_SECURITY_STATIC_ANALYZER_CHECKS_EXTENDED == "true" && $DEV_LANGUAGE == "python" && $CI_PIPELINE_SOURCE == "merge_request_event"'
    - if: '$CI_SECURITY_STATIC_ANALYZER_CHECKS_EXTENDED == "true" && $DEV_LANGUAGE == "python" && $CI_COMMIT_TAG == ""'

.security-static-analyzer-javascript:
  stage: security-checks
  extends: .security-checks
  script:
    - npm run lint:security
  rules:
    - if: '$CI_SECURITY_STATIC_ANALYZER_CHECKS == "true" && $DEV_LANGUAGE == "javascript" && $CI_PIPELINE_SOURCE == "merge_request_event"'
    - if: '$CI_SECURITY_STATIC_ANALYZER_CHECKS == "true" && $DEV_LANGUAGE == "javascript" && $CI_COMMIT_TAG == ""'

.security-git-secrets-checks:
  stage: security-checks
  extends: .security-checks
  image: zricethezav/gitleaks:v8.18.2
  script:
    - gitleaks detect --no-git -v --source=. --baseline-path gitleaks-report.json --report-path findings.json
    - |
      if [[ $? != 0 ]]; then
        echo Call "docker run --rm -v /path/to/repo:/sources  -w /sources zricethezav/gitleaks:v8.18.2 detect --no-git --report-path gitleaks-report.json --source=." in the root of repository and commit gitleaks-report.json if there is false-positive error.
        exit 1
      fi
  artifacts:
    when: on_failure
    paths:
      - findings.json
    expire_in: 1 week
  rules:
    - if: '$CI_SECURITY_GIT_SECRETS_CHECKS == "true" && $CI_PIPELINE_SOURCE == "merge_request_event"'
    - if: '$CI_SECURITY_GIT_SECRETS_CHECKS == "true" && $CI_PIPELINE_SOURCE == "pipeline"'
    - if: '$CI_SECURITY_GIT_SECRETS_CHECKS == "true" && $CI_COMMIT_TAG == ""'
