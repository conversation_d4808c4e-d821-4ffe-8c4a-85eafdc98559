---
.deploy:
  image: $CD_IMAGE
  stage: deploy
  variables:
    GIT_STRATEGY: $CD_GIT_STRATEGY
  before_script: |
    if [ ! -z "${SCRIPTS_ROOT}" ]; then
      mkdir -p ${SCRIPTS_ROOT}
      curl -H "PRIVATE-TOKEN:$GITOPS_PIPELINE_TOKEN" --create-dirs "$CI_API_V4_URL/projects/$CI_ARTIFACTS_REPO/repository/archive?path=$CI_VALUES_PATH&sha=${CI_GITOPS_MONOREPO_BRANCH:-master}" -o /tmp/archive.tar.gz
      tar zxf /tmp/archive.tar.gz -C ${SCRIPTS_ROOT}  --strip-components 1
      curl -H "PRIVATE-TOKEN:$GITOPS_PIPELINE_TOKEN" --create-dirs "$CI_API_V4_URL/projects/$CI_ARTIFACTS_REPO/repository/archive?path=$CI_SCRIPTS_PATH&sha=${CI_GITOPS_MONOREPO_BRANCH:-master}" -o /tmp/archive.tar.gz
      tar zxf /tmp/archive.tar.gz -C ${SCRIPTS_ROOT} --strip-components 1
    fi;
  after_script: python3 ${SCRIPTS_ROOT}${CI_SCRIPTS_PATH}/jellyfish.py update
  script:
    - |
      retry_command() {
          local command="$1"
          local max_attempts="$2"
          local attempt=1

          while [ $attempt -le $max_attempts ]; do
              echo "Attempt $attempt: $command"
              set +e
              eval "$command"
              exit_code=$?
              set -e

              if [ $exit_code -eq 0 ]; then
                  echo "Command succeeded on attempt $attempt."
                  return 0
              else
                  echo "Command failed on attempt $attempt (Exit code: $exit_code). Retrying..."
                  random_number=$(( $RANDOM % 5 )) # sleep random seconds below 5s
                  sleep ${random_number}
              fi

              attempt=$((attempt + 1))
          done

          echo "Command failed after $max_attempts attempts."
          exit 1
      }
      send_slack_notification() {
        NOTIFICATION_MESSAGE=$1
        URL=https://slack.com/api/chat.postMessage
        curl -X POST -H "Authorization: Bearer $SLACK_TOKEN" -H 'Content-type: application/json' --data "{\"text\":\"$NOTIFICATION_MESSAGE\", \"channel\": \"$SLACK_NOTIFICATION_CHANNEL\",\"username\":\"gcp-service-deployment\"}" $URL
      }
      send_deployment_status() {
        EXIT_CODE=$? # 0 = success, 1 = failed
        case ${EXIT_CODE} in
          0)
            STATUS="Success :white_check_mark:"
            ;;
          *)
            STATUS="Failed :alert:"
            ;;
        esac
        send_slack_notification "${STATUS} to deploy service: *<${ARGO_APP_URL}|${RELEASE_NAME}>* \n <$CI_PIPELINE_URL|Pipeline Link>"
      }
      present_ingress_domain() {
        # Show domains ingress of this argocd app
        retry_command "argocd --plaintext --insecure app manifests --source live ${RELEASE_NAME} > /tmp/${RELEASE_NAME}-manifest.yaml" $ARGO_RETRY_MAX_ATTEMPTS
        echo -e "\n+ Domain ingress for this ArgoCD Application (preview / non-preview deployment): ${RELEASE_NAME}"
        echo "|"
        cat /tmp/${RELEASE_NAME}-manifest.yaml | python3 "${CI_PROJECT_DIR}/${SCRIPTS_ROOT}scripts/extract_ingress_domains.py"
      }

    - |
      if [[ $RELEASE_CHANNEL == "preview" ]]; then # If merge request
        export PREVIEW_SUBPATH="preview/${CI_MERGE_REQUEST_IID}/"
        if [[ -z $MIGRATION_ARGOCD_NAMING ]] || [[ ! -z $MIGRATION_ARGOCD_NAMING && $MIGRATION_ARGOCD_NAMING == "False" ]];then
          export RELEASE_NAME=pr-${APP_NAME}-${PROJECT}-${ENV}-${CI_MERGE_REQUEST_IID}
        else
          export RELEASE_NAME=pr-${APP_NAME}-${ENV}-${CI_MERGE_REQUEST_IID}
        fi
      else
        export PREVIEW_SUBPATH="chart/"
        if [[ -z $MIGRATION_ARGOCD_NAMING ]] || [[ ! -z $MIGRATION_ARGOCD_NAMING && $MIGRATION_ARGOCD_NAMING == "False" ]];then
          export RELEASE_NAME=${APP_NAME}-${PROJECT}-${ENV}
        else
          export RELEASE_NAME=${APP_NAME}-${ENV}
        fi
      fi

      export ARGO_APP_URL="${ARGOCD_SERVER_HTTP_URL}/applications/${RELEASE_NAME}"

      send_slack_notification "Start to deploy service: *<${ARGO_APP_URL}|${RELEASE_NAME}>* \n <$CI_PIPELINE_URL|Pipeline Link>"
      trap 'send_deployment_status' EXIT
    - gcloud auth configure-docker ${HELM_GAR_LOCATION}-docker.pkg.dev --quiet
    - helm pull oci://${HELM_GAR_LOCATION}-docker.pkg.dev/${HELM_GAR_PROJECT}/${HELM_GAR_REPOSITORY}/${RELEASE_CHANNEL}/${APP_NAME} --version ${GitVersion_SemVer}
    - HELM_CHART_VALUES="${SCRIPTS_ROOT}values/${CHART_TYPE:-saas}/${APP_NAME}/${VARIANT}.yaml"
    - KUSTOMIZATION_GENERATE_SCRIPT="${SCRIPTS_ROOT}scripts/kustomize_generator.py"
    - git clone --depth 3 --recurse-submodules --jobs=4 https://${CI_USER}:${GITOPS_PIPELINE_TOKEN}@${CI_SERVER_HOST}/devops/gitops-cd.git
    - CHART_PACKAGE="${APP_NAME}-${GitVersion_SemVer}.tgz"
    - PROJECT_PATH="projects/${ENV}/${PROJECT}/${APP_NAME}/${PREVIEW_SUBPATH}"
    - PROJECT_ROOT_PATH="gitops-cd/${PROJECT_PATH}"
    - mkdir -p $PROJECT_ROOT_PATH
    - rm -rf $PROJECT_ROOT_PATH/*
    - tar -xvzf $CHART_PACKAGE -C $PROJECT_ROOT_PATH --strip-components=1
    - rm $CHART_PACKAGE
    - cp $HELM_CHART_VALUES $PROJECT_ROOT_PATH/ops-values.yaml
    - if [[ ! -z "$ASSETS_PATH" ]] && [[ ! -z "$ASSETS_DST_PATH" ]]; then
    - mkdir -p gitops-cd${ASSETS_DST_PATH}/${PROJECT}
    - cp -rf ${SCRIPTS_ROOT}${ASSETS_PATH}/${PROJECT} gitops-cd/${ASSETS_DST_PATH}/
    - fi
    - cd gitops-cd
    - git add $PROJECT_PATH
    - if [ ! -n "$(git status --porcelain)" ]; then
    - echo "No diff.";
    - retry_command "argocd --plaintext --insecure login $ARGOCD_SERVER_URL  --username $ARGOCD_USERNAME --password $ARGOCD_PASSWORD" $ARGO_RETRY_MAX_ATTEMPTS
    - present_ingress_domain
    - exit 0
    - fi
    - if [[ ! -z "$ASSETS_PATH" ]] && [[ ! -z "$ASSETS_DST_PATH" ]]; then
    - git add ${ASSETS_DST_PATH}
    - fi
    - echo "Configuration changes tracked, pushing to repo"
    - echo "Git setup"
    - git config --global user.name $GITLAB_USER_LOGIN
    - git config --global user.email $GITLAB_USER_EMAIL
    - git diff
    - git status
    - echo "Commiting changes ...";
    - |
      git commit -m "${CI_COMMIT_REF_NAME}: [auto] ${CI_COMMIT_MESSAGE}
      Project: $CI_PROJECT_URL
      Pipeline: $CI_PIPELINE_URL
      Job url: $CI_JOB_URL"
    - set +e
    - git push --set-upstream origin; exit_git_push=$?
      # Avoid same gitops push in other steps let's this behind of HEAD
    - |
      retry_times=1
      while [[ ${retry_times} -le ${GITOPS_PUSH_MAX_ATTEMPTS} && ${exit_git_push} -ne 0 ]]
      do
      random_number=$(( $RANDOM % 5 )) # sleep random seconds below 5s
      sleep ${random_number}
      echo "Retrying git push: $retry_times times"
    - git fetch
    - git pull -r
    - git push --set-upstream origin; exit_git_push=$?
    - retry_times=$(( $retry_times + 1 ))
    - done
    - |
      if [ $exit_git_push -ne 0 ]; then
        echo "Failed to push changes to gitops repo"
        exit 1
      fi
    - set -e
    - retry_command "argocd --plaintext --insecure login $ARGOCD_SERVER_URL  --username $ARGOCD_USERNAME --password $ARGOCD_PASSWORD" $ARGO_RETRY_MAX_ATTEMPTS
    - argocd --plaintext --insecure app terminate-op ${RELEASE_NAME} || echo 'Nothing to terminate'
    - argocd --plaintext --insecure app delete-resource ${RELEASE_NAME} --kind Job || echo 'Nothing to delete'
    - retry_command "argocd --plaintext --insecure app get --hard-refresh ${RELEASE_NAME}" $ARGO_RETRY_MAX_ATTEMPTS
    - retry_command "argocd --plaintext --insecure app wait ${RELEASE_NAME} --timeout ${ARGOCD_SYNC_WAIT_TIMEOUT:-600}" 3
    - present_ingress_domain
    - |
      if [[ ${UPSTREAM_CI_INTEGRATION_TEST_ENABLE} == "true" ]];then
        # We give a gap time to allow deployment to be ready completely before run trigger integration test from AQA repository
        sleep 60
      fi
  tags:
    - gke-ant
  retry:
    max: 1
    when:
      - api_failure
      - runner_system_failure
      - scheduler_failure
      - unknown_failure
      - stale_schedule
  allow_failure: false

.stop-preview:
  extends: .deploy
  stage: cleanup
  script:
    - git clone --depth 3 --recurse-submodules --jobs=4 https://${CI_USER}:${GITOPS_PIPELINE_TOKEN}@${CI_SERVER_HOST}/devops/gitops-cd.git
    - |
      export PREVIEW_SUBPATH="preview/${CI_COMMIT_REF_SLUG:-nothing}/"
      PROJECT_PATH="projects/${ENV}/${PROJECT}/${APP_NAME}/${PREVIEW_SUBPATH}"
      PROJECT_ROOT_PATH="gitops-cd/${PROJECT_PATH}"
      cd gitops-cd
      echo "Git setup"
      git config --global user.name $GITLAB_USER_LOGIN
      git config --global user.email $GITLAB_USER_EMAIL
      git rm -r ${PROJECT_PATH}
      git diff
      git status
      echo "Commiting changes ...";
      git commit -a -m "${CI_COMMIT_REF_NAME}: [auto] Cleaup preview.
      Project: $CI_PROJECT_URL
      Pipeline: $CI_PIPELINE_URL
      Job url: $CI_JOB_URL"

      set +e
      git push --set-upstream origin; exit_git_push=$?

      # Avoid same gitops push in other steps let's this behind of HEAD
      retry_times=1
      while [[ ${retry_times} -le ${GITOPS_PUSH_MAX_ATTEMPTS} && ${exit_git_push} -ne 0 ]]
      do
        random_number=$(( $RANDOM % 5 )) # sleep random seconds below 5s
        sleep ${random_number}
        echo "Retrying git push : $retry_times times"
        git fetch
        git pull -r
        git push --set-upstream origin; exit_git_push=$?
        retry_times=$(( $retry_times + 1 ))
      done
      set -e
  environment:
    name: preview/$APP_NAME-$PROJECT-$CI_COMMIT_REF_NAME
    action: stop
  allow_failure: true
  rules:
    - if: $CI_PIPELINE_SOURCE == 'merge_request_event' && $CI_MERGE_REQUEST_LABELS =~ /preview/
      when: manual
