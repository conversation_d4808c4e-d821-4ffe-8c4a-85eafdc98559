.sbom-scanning:
  stage: build
  image:
    name: $CI_SBOM_IMAGE
  dependencies:
    - dockerize
  needs:
    - dockerize
  allow_failure: true
  script:
    - |
      # SBOM Report for Docker Image
      bash /app/sbom-script.sh -a ${APP_NAME} -t image -i ${APP_IMAGE} -r ${RELEASE_CHANNEL}

      # SBOM Report for App codebase
      bash /app/sbom-script.sh -a ${APP_NAME} -t app -l ${DEV_LANGUAGE} -r ${RELEASE_CHANNEL}
  retry:
    max: 1
    when:
      - api_failure
      - runner_system_failure
  tags:
    - gke-docker-gcp-ant
  rules:
    - if: "$DOCKER_BUILD_ONLY"
      when: never
    - if: '$CHART_TYPE == "saas" && $CI_COMMIT_REF_NAME =~ /^release-v\d+\.\d+\.\d+$/'
    - if: '$CHART_TYPE == "saas" && $CI_COMMIT_TAG  =~ /^v\d+\.\d+\.\d+$/'
    - if: '$CHART_TYPE == "saas" && $CI_COMMIT_REF_NAME == $CI_DEFAULT_BRANCH'
