.publish:
  image: $CD_IMAGE
  stage: build
  variables:
    HELM_API_VERSIONS_ARGS: "--api-versions monitoring.coreos.com/v1"
  before_script:
    - |
      if [ ! -z "${SCRIPTS_ROOT}" ]; then
        echo "Running outside of ${SCRIPTS_ROOT}. Downloading requirements."
        curl -H "PRIVATE-TOKEN:$GITOPS_PIPELINE_TOKEN" --create-dirs "$CI_API_V4_URL/projects/$CI_ARTIFACTS_REPO/repository/archive?path=$CI_SCRIPTS_PATH&sha=${CI_GITOPS_MONOREPO_BRANCH:-master}" -o /tmp/archive.tar.gz
        mkdir -p ${SCRIPTS_ROOT}
        tar zxf /tmp/archive.tar.gz -C ${SCRIPTS_ROOT} --strip-components 1
      fi;
  script:
    - if [[ $CHART_TYPE == "saas" ]]; then python3 ${SCRIPTS_ROOT}scripts/set_helm_version.py; fi
    - gcloud auth configure-docker ${HELM_GAR_LOCATION}-docker.pkg.dev --quiet
    - mkdir -p build/
    - set -x
    - helm dependencies update ${HELM_CHART}
    - helm dependencies build ${HELM_CHART}
    - helm lint ${HELM_CHART}
    - helm template $APP_NAME ${HELM_CHART} ${HELM_API_VERSIONS_ARGS}
    - helm package ${HELM_CHART} -u --version=${GitVersion_SemVer} --app-version=${CI_COMMIT_SHA} --destination build/
    - ls -al build/
    - helm push build/${APP_NAME}-${GitVersion_SemVer}.tgz oci://${HELM_GAR_LOCATION}-docker.pkg.dev/${HELM_GAR_PROJECT}/${HELM_GAR_REPOSITORY}/${RELEASE_CHANNEL}
  tags:
    - gke-ant
  retry:
    max: 1
    when: runner_system_failure
  rules:
    - if: "$DOCKER_BUILD_ONLY"
      when: never
    - if: "$CI_COMMIT_REF_NAME == $CI_DEFAULT_BRANCH"
    - if: $CI_MERGE_REQUEST_IID
    - if: '$CI_COMMIT_REF_NAME =~ /^release-v\d+\.\d+\.\d+$/'
    - if: '$CI_COMMIT_TAG  =~ /^v\d+\.\d+\.\d+$/'
