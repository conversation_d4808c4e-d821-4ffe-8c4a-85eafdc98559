variables:
  FF_USE_LEGACY_KUBERNETES_EXECUTION_STRATEGY: "false"
  GITOPS_PUSH_MAX_ATTEMPTS: 10
  ARGO_RETRY_MAX_ATTEMPTS: 10
  CI_ARTIFACTS_REPO: "712"
  CI_SCRIPTS_PATH: scripts
  CI_CHARTS_PATH: charts
  CI_VALUES_PATH: values
  KANIKO_IMAGE_CACHING_TTL: "672h"
  KANIKO_CACHING_ENABLED: "true"
  SECURITY_SCAN_IMAGE: asia-docker.pkg.dev/inspectorio-ant/platform-docker-images/stable/bandit/bandit:v1.7.4-3
  CI_GITOPS_MONOREPO_BRANCH: master
  CI_BUILD_IMAGE: asia-docker.pkg.dev/inspectorio-ant/platform-docker-images/stable/kaniko/executor:1.0.0-dev-1b9d3433
  CD_IMAGE: asia-docker.pkg.dev/inspectorio-ant/platform-docker-images/stable/deploy-tools/cd-image:1.0.0-dev-c61b9805
  CI_SBOM_IMAGE: asia-docker.pkg.dev/inspectorio-ant/platform-docker-images/stable/platform/sbom-tools:1.0.0-dev-c8768924
  CI_INTEGRATION_TEST_ENABLE: "false"
  CI_INTEGRATION_TEST_BRANCH: "master"
  CI_INTEGRATION_TEST_PROJECT: "automation-test/unified-health-check" # default value
  NPM_REGISTRY_PROJECT: inspectorio-ant
  NPM_REGISTRY_PROJECT_REGION: asia
  CHART_TYPE: saas
  LOGIN_GCP_NPM_REGISTRY: "false"
  HELM_CHART: chart/
  APP_NAME: $CI_PROJECT_NAME
  NGINX_FRONTEND_BASE_IMAGE: asia-docker.pkg.dev/inspectorio-ant/platform-docker-images/stable/nginx/frontend:1.0.0-dev-04167b10
  SLACK_NOTIFICATION_CHANNEL: CRQ45NH1S
  ARGOCD_SERVER_HTTP_URL: https://argocd.gcp-ant.inspectorio.com
  CD_GIT_STRATEGY: none
  SBOM_API_URL: "http://dependency-track-apiserver.dependency-track.svc.cluster.local/api/v1/bom"
  ARGOCD_SERVER_URL: "argocd-server.argocd.svc.cluster.local"
  CI_REDOCLY_IMAGE: asia-docker.pkg.dev/inspectorio-ant/platform-docker-images/stable/redocly/redocly-cli:v1.0.2
  OPENAPI_PATH: spec/openapi
  APICURIO_BASE_URL: http://apicurio-apicurio-registry.apicurio.svc.cluster.local:8080
  OPENAPI_SLACK_NOTIFICATION_CHANNEL: "C072DJ2EF9V"
  ARTIFACT_REGISTRY_LOCATION: "asia"
  ARTIFACT_REGISTRY_PROJECT_ID: "inspectorio-ant"
  ARTIFACT_REGISTRY_REPOSITORY: "saas"
  GCLOUD_NODE_IMAGE: "asia-docker.pkg.dev/inspectorio-ant/platform-docker-images/stable/gcloud-node/node-v16:1.0.0-dev-f6a716fd"
