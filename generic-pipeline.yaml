#Common pipeline with GitOps approach

include:
  - local: "vars.yaml"
  - local: "init_library.yaml"
  - local: "ci.yaml"
    rules:
      - if: $JOB_ACTION == null && $CI_BYPASS == null
  - local: "cd.yaml"
    rules:
      - if: $JOB_ACTION == null
  - local: "utils.yaml"
    rules:
      - if: $JOB_ACTION != null
  - local: "preview.yaml"
    rules:
      - if: "$CI_MERGE_REQUEST_IID && $CI_MERGE_REQUEST_LABELS =~ /preview/"
  - local: "common.yaml"
    rules:
      - if: "$CI_MERGE_REQUEST_IID && $CI_MERGE_REQUEST_LABELS !~ /preview/"
      - if: $CI_TAG
      - if: $CI_MERGE_REQUEST_IID == null

stages:
  - utils
  - commits-check
  - security-checks
  - login-gcp-registry
  - lint
  - build
  - test
  - tests
  - test-report
  - diff
  - release
  - delivery
  - post-tests
  - cleaup

determine-version:
  extends: .gitversion_function
