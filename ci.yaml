include:
  - local: "library/*.yaml"

### Linters
pylint-checks:
  extends: .python_unit_tests
  stage: lint
  script:
    - pip install pylint
    - pip install poetry
    - export PYTHONPATH="${PYTHONPATH}:$(pwd)/src"
    - poetry install
    - poetry run pylint src
  rules:
    - if: $CI_LINT_CHECKS == "true" && $CI_PIPELINE_SOURCE == "merge_request_event" && $DEV_LANGUAGE == "python" && $CI_COMMIT_MESSAGE !~ /.*\/skip-tests.*/

nodejs-lint-checks:
  extends: .nodejs_unit_tests
  stage: lint
  script:
    - npm ci
    - npm run lint
  rules:
    - if: $CI_LINT_CHECKS == "true" && $CI_PIPELINE_SOURCE == "merge_request_event" && $DEV_LANGUAGE == "javascript" && $CI_COMMIT_MESSAGE !~ /.*\/skip-tests.*/

######
npm-registry:
  extends: .login-npm-registry
  stage: login-gcp-registry
  script:
    - rm -f package-lock.json package.json
    - npm i -g google-artifactregistry-auth
    - gcloud artifacts print-settings npm --project=${NPM_REGISTRY_PROJECT} --repository=${NPM_REPOSITORY_NAME} --location=${NPM_REGISTRY_PROJECT_REGION} --scope=${NPM_REGISTRY_PROJECT_SCOPE} >> .npmrc
    - npx google-artifactregistry-auth .npmrc
  artifacts:
    paths:
      - .npmrc
    when: always
  variables:
    GIT_STRATEGY: fetch
  rules:
    - if: $DEV_LANGUAGE == "javascript" && $LOGIN_GCP_NPM_REGISTRY == "true"

######
security-cve-checks-python:
  extends: .security-cve-checks-python

security-cve-checks-javascript:
  extends: .security-cve-checks-javascript

security-static-analyzer-python:
  extends: .security-static-analyzer-python

security-static-analyzer-python-extended:
  extends: .security-static-analyzer-python-extended

security-static-analyzer-javascript:
  extends: .security-static-analyzer-javascript

security-git-secrets-checks:
  extends: .security-git-secrets-checks

###
publish-release:
  extends: .publish

dockerize:
  extends: .dockerize

sbom-scanning:
  extends: .sbom-scanning

openapi-schema-build:
  extends: .openapi-schema-build

openapi-schema-publish:
  extends: .openapi-schema-publish
